import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class FollowingPhotoIdsNotifier extends IdListNotifier {}

final followingPhotoIdsProvider =
    NotifierProvider.autoDispose<FollowingPhotoIdsNotifier, List<int>>(
      FollowingPhotoIdsNotifier.new,
    );

final followingPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(followingPhotoIdsProvider);
  if (ids.isEmpty) return <PhotoData>[];

  return ref.read(photoStoreProvider.notifier).getItems(ids);
});

/// High-level service for managing following photos's reactivity.
/// This is the recommended way to manage following photos.
final class FollowingPhotosReactiveService {
  const FollowingPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get following photos list
  List<PhotoData> getAll() {
    return ref.read(followingPhotosProvider);
  }

  /// Get following photo IDs
  List<int> getAllIds() {
    return ref.read(followingPhotoIdsProvider);
  }

  /// Add a new following photo (adds to both global store and following list)
  void addItem(PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to following photos list
    ref.read(followingPhotoIdsProvider.notifier).addItem(photo.id);
  }

  /// Add multiple following photos
  void addItems(List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to following photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(followingPhotoIdsProvider.notifier).addItems(photoIds);
  }

  /// Remove a following photo
  void remove(int photoId) {
    // Remove from following photos list
    ref.read(followingPhotoIdsProvider.notifier).removeItem(photoId);
  }

  /// Remove multiple following photos
  void removeItems(List<int> photoIds) {
    if (photoIds.isEmpty) return;

    // Remove from following photos list
    ref.read(followingPhotoIdsProvider.notifier).removeItems(photoIds);
  }

  /// Update a photo in the store (automatically reflects in following list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all following photos
  void replaceAll(List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace following photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(followingPhotoIdsProvider.notifier).replaceAll(photoIds);
  }

  /// Clear all following photos
  void clear() {
    // Clear the following photos list
    ref.read(followingPhotoIdsProvider.notifier).clear();
  }

  /// Get following photos count
  int get followingCount => ref.read(followingPhotoIdsProvider.notifier).length;

  /// Check if following list is empty
  bool get isEmpty => ref.read(followingPhotoIdsProvider.notifier).isEmpty;

  /// Check if following list is not empty
  bool get isNotEmpty =>
      ref.read(followingPhotoIdsProvider.notifier).isNotEmpty;
}

/// Provider for the FollowingPhotosReactiveService.
final followingPhotosReactiveServiceProvider =
    Provider.autoDispose<FollowingPhotosReactiveService>((ref) {
      return FollowingPhotosReactiveService(ref);
    });
