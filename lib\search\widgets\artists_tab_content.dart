// artists_tab_content.dart

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/http_responses/artist_list_response.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/services/artist_list_service.dart';
import 'package:portraitmode/artist/widgets/artist_list_item.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/search/dto/search_data.dart';
import 'package:portraitmode/search/misc.dart';
import 'package:portraitmode/search/providers/search_artists_data_provider.dart';
import 'package:portraitmode/search/providers/search_artists_provider.dart';

class ArtistsTabContent extends ConsumerStatefulWidget {
  const ArtistsTabContent({
    super.key,
    required this.searchData,
    required this.keyword,
    required this.dataList,
  });

  final ArtistsSearchData searchData;
  final String keyword;
  final List<ArtistData> dataList;

  @override
  ArtistsTabContentState createState() => ArtistsTabContentState();
}

class ArtistsTabContentState extends ConsumerState<ArtistsTabContent> {
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  late final int _profileId;

  final _artistListService = ArtistListService();
  final int _loadMorePerPage = LoadMoreConfig.smallItemsPerPage;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();

    _profileId = LocalUserService.userId ?? 0;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isLoadingMore && !widget.searchData.loadMoreEndReached;
  }

  /// Trigger load more.
  ///
  /// The `_canLoadMore` checking should be in the caller, not here.
  void _triggerLoadMore() async {
    if (mounted) {
      setState(() {
        _isLoadingMore = true;
      });

      SearchLoadMoreNotification(
        artistSearchScreen: _isFirstLoad()
            ? SearchScreenLoadMoreStatus.doingFirstLoadMore
            : SearchScreenLoadMoreStatus.doingLoadMore,
      ).dispatch(context);
    }

    await _handleLoadMore();

    if (mounted) {
      setState(() {
        _isLoadingMore = false;
      });

      SearchLoadMoreNotification(
        artistSearchScreen: SearchScreenLoadMoreStatus.idle,
      ).dispatch(context);
    }
  }

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    // log('build screen: ArtistsTabContent');
    log('The artists search keyword param is: "${widget.keyword}"');

    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    return Container(
      constraints: const BoxConstraints(maxWidth: 768.0),
      child: RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: _handleRefresh,
        child: NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            final double triggerPoint = _getLoadMoreTriggerPoint(
              scrollInfo.metrics.maxScrollExtent,
            );

            // Handle load more when scrolling reaches the trigger point
            if (scrollInfo.metrics.pixels >= triggerPoint) {
              if (_canLoadMore()) {
                log('🔥 TRIGGERING LOAD MORE!');

                log('Scroll Info:');
                log('  current position: ${scrollInfo.metrics.pixels}');
                log('  maxScrollExtent: ${scrollInfo.metrics.maxScrollExtent}');
                log('  trigger point: $triggerPoint');
                log(
                  '  remaining content: ${scrollInfo.metrics.maxScrollExtent - triggerPoint}',
                );

                _triggerLoadMore();
              }
            }

            return false;
          },
          child: ListView.builder(
            // Remove the controller - let NestedScrollView handle scrolling
            cacheExtent: _cacheExtent,
            itemCount: widget.dataList.length,
            itemBuilder: (BuildContext context, int index) {
              final int artistId = widget.dataList[index].id;

              return Padding(
                key: ValueKey(artistId),
                padding: EdgeInsets.only(
                  left: ScreenStyleConfig.horizontalPadding,
                  right: ScreenStyleConfig.horizontalPadding,
                  top: index == 0 ? LayoutConfig.contentTopGap : 12.0,
                ),
                child: ArtistListItem(
                  index: index,
                  artist: widget.dataList[index],
                  isOwnProfile: artistId == _profileId,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  bool _isFirstLoad() {
    return widget.searchData.loadMoreLastId == -1 ||
        widget.searchData.loadMoreLastId == 0;
  }

  Future<void> _handleRefresh() async {
    // Reset loading state
    _isLoadingMore = false;

    late ArtistListResponse response;

    // log('keyword onRefresh: ${searchData.keyword}');

    if (widget.keyword.isNotEmpty) {
      response = await _artistListService.search(
        keyword: widget.keyword,
        limit: _loadMorePerPage,
        lastId: null,
        lastTotalPhotos: null,
      );
    } else {
      response = await _artistListService.fetch(
        limit: _loadMorePerPage,
        lastId: null,
        lastTotalPhotos: null,
      );
    }

    _handleArtistListResponse(response, true, false);
  }

  Future<void> _handleLoadMore() async {
    late ArtistListResponse response;

    // log('The artists search keyword is: "${widget.keyword}"');

    if (widget.keyword.isNotEmpty) {
      response = await _artistListService.search(
        keyword: widget.keyword,
        limit: _loadMorePerPage,
        lastId: widget.searchData.loadMoreLastId,
        lastTotalPhotos: widget.searchData.lastTotalPhotos,
      );
    } else {
      response = await _artistListService.fetch(
        limit: _loadMorePerPage,
        lastId: widget.searchData.loadMoreLastId,
        lastTotalPhotos: widget.searchData.lastTotalPhotos,
      );
    }

    _handleArtistListResponse(response, false, _isFirstLoad());
  }

  void _handleArtistListResponse(
    ArtistListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final searchArtistsReactiveService = ref.read(
      searchArtistsReactiveServiceProvider,
    );

    if (response.data.isEmpty) {
      if (isRefresh) {
        searchArtistsReactiveService.clear();
      }

      ref.read(searchArtistsDataProvider.notifier).setLoadMoreEndReached(true);
      return;
    }

    ref
        .read(artistStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    ref
        .read(searchArtistsDataProvider.notifier)
        .updateSomeValues(
          loadMoreLastId: response.data.last.id,
          lastTotalPhotos: response.data.last.totalPhotos,
        );

    if (isRefresh) {
      searchArtistsReactiveService.replaceAll(response.data);
    } else {
      if (isFirstLoad) {
        searchArtistsReactiveService.replaceAll(response.data);
      } else {
        searchArtistsReactiveService.addItems(response.data);
      }
    }

    if (response.data.length < _loadMorePerPage) {
      ref.read(searchArtistsDataProvider.notifier).setLoadMoreEndReached(true);
    } else {
      ref.read(searchArtistsDataProvider.notifier).setLoadMoreEndReached(false);
    }
  }
}
