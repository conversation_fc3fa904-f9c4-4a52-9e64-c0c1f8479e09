import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_metadata_item.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/like_icon_button.dart';

class PhotoMetadata extends ConsumerWidget {
  final PhotoData photo;
  final EdgeInsetsGeometry? padding;
  final bool hasDivider;
  final double contentToDividerGap;

  const PhotoMetadata({
    super.key,
    required this.photo,
    this.padding,
    this.hasDivider = true,
    this.contentToDividerGap = 8.0,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const double rowGap = 13.0;

    DateFormat dateFormat = DateFormat('MMM d, yyyy');
    DateTime uploadDate = DateTime.parse(photo.date);

    return Padding(
      padding: padding != null ? padding! : const EdgeInsets.all(0.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Details",
            style: TextStyle(
              fontSize: 15.0,
              // fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: rowGap),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PhotoMetadataItem(
                label: 'Published',
                value: dateFormat.format(uploadDate),
              ),
              PhotoMetadataItem(
                label: 'Likes',
                child: LikeIconButton(
                  photoId: photo.id,
                  isLiked: photo.isLiked,
                  totalLikes: photo.totalLikes,
                  showCount: true,
                  showText: true,
                  fontSize: 13.0,
                  iconSize: 20.0,
                  shrinkTapTarget: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: rowGap),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PhotoMetadataItem(
                label: 'Camera',
                value: (photo.camera.isNotEmpty ? photo.camera : '-'),
              ),
              PhotoMetadataItem(
                label: 'Location',
                child: Wrap(
                  children: [
                    Text(photo.address, style: const TextStyle(fontSize: 13.0)),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: rowGap),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PhotoMetadataItem(
                label: 'Focal length',
                value:
                    (photo.focalLength.isNotEmpty && photo.focalLength != '0')
                    ? '${photo.focalLength}mm'
                    : "-",
              ),
              PhotoMetadataItem(
                label: 'Shutter speed',
                value: photo.shutterSpeed.isNotEmpty ? photo.shutterSpeed : "-",
              ),
            ],
          ),
          const SizedBox(height: rowGap),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PhotoMetadataItem(
                label: 'Aperture',
                value: photo.aperture.isNotEmpty && photo.aperture != '0'
                    ? 'F/${photo.aperture}'
                    : "-",
              ),
              PhotoMetadataItem(
                label: 'ISO',
                value: photo.shutterSpeed.isNotEmpty ? photo.iso : "-",
              ),
            ],
          ),
          const SizedBox(height: rowGap),
          if (hasDivider) SizedBox(height: contentToDividerGap),
          if (hasDivider)
            Divider(height: 1.0, color: context.colors.borderColor),
        ],
      ),
    );
  }
}
