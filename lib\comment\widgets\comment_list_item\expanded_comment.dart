import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/http_responses/comment_list_response.dart';
import 'package:portraitmode/comment/providers/comment_store_provider.dart';
import 'package:portraitmode/comment/services/comment_list_service.dart';
import 'package:portraitmode/comment/utils/delete_comment_util.dart';
import 'package:portraitmode/comment/utils/edit_comment_util.dart';
import 'package:portraitmode/comment/widgets/comment_list_item.dart';
import 'package:portraitmode/comment/widgets/comment_list_item/replies_not_found.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';

class ExpandedComment extends ConsumerStatefulWidget {
  final TextEditingController commentFieldController;
  final FocusNode commentFieldFocusNode;
  final CommentData comment;
  final GlobalKey? activeCommentKey;
  final bool shouldLoadReplies;
  final VoidCallback onClose;

  const ExpandedComment({
    super.key,
    required this.commentFieldController,
    required this.commentFieldFocusNode,
    required this.comment,
    this.activeCommentKey,
    this.shouldLoadReplies = false,
    required this.onClose,
  });

  @override
  ExpandedCommentState createState() => ExpandedCommentState();
}

class ExpandedCommentState extends ConsumerState<ExpandedComment> {
  final CommentListService _commentListService = CommentListService();
  late String _replyWording;

  late final int _profileId;

  @override
  void initState() {
    _profileId = LocalUserService.userId ?? 0;
    _replyWording = widget.comment.totalReplies > 1 ? "replies" : "reply";

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<CommentListResponse>(
      future: _fetchReplies(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingIndicator();
        }

        if (snapshot.hasError) {
          return Text('Error loading replies');
        }

        if (snapshot.hasData) {
          return _buildReplyList();
        }

        return _buildLoadingIndicator();
      },
    );
  }

  Widget _buildLoadingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(left: 4.0),
      child: Text(
        'Loading $_replyWording...',
        style: TextStyle(fontSize: 12.0, color: context.colors.darkerGreyColor),
      ),
    );
  }

  Widget _buildReplyList() {
    if (widget.comment.replies.isEmpty) {
      return const RepliesNotFound();
    }

    _replyWording = widget.comment.replies.length > 1 ? "replies" : "reply";

    return Padding(
      padding: const EdgeInsets.only(left: 0.0, top: 4.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (var i = 0; i < widget.comment.replies.length; i++)
            if (i <= widget.comment.replies.length - 1)
              _buildReplyItem(widget.comment.replies[i], i),
        ],
      ),
    );
  }

  Widget _buildReplyItem(CommentData reply, int index) {
    CommentData parentComment = widget.comment;

    if (widget.comment.id != reply.parentId) {
      CommentData? parent = widget.comment.replies.firstWhereOrNull(
        (item) => item.id == reply.parentId,
      );

      if (parent != null) {
        parentComment = parent;
      }
    }

    final bool isOwnComment = reply.authorId == _profileId;

    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: CommentListItem(
        commentFieldController: widget.commentFieldController,
        commentFieldFocusNode: widget.commentFieldFocusNode,
        comment: reply,
        parentComment: parentComment,
        topLevelParentComment: widget.comment,
        isOwnComment: isOwnComment,
        onEditCommentTap: isOwnComment
            ? () => handleEditCommentTap(
                ref: ref,
                comment: reply,
                commentFieldController: widget.commentFieldController,
                commentFieldFocusNode: widget.commentFieldFocusNode,
              )
            : null,
        onDeleteCommentTap: isOwnComment
            ? () async {
                if (widget.activeCommentKey == null) {
                  return;
                }

                await DeleteCommentUtil(
                  context: context,
                  ref: ref,
                  photoId: widget.comment.postId,
                  commentToDelete: reply,
                  targetKey: widget.activeCommentKey!,
                ).handleDeleteEvent();
              }
            : null,
      ),
    );
  }

  Future<CommentListResponse> _fetchReplies() async {
    CommentListResponse response = await _commentListService.fetch(
      parentId: widget.comment.id,
    );

    if (!mounted) return response;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      }

      return response;
    }

    if (response.data.isNotEmpty) {
      // Sort the array before saving it (last comes first).
      // Well, actually we don't need to sort it here
      // because we will just add/update them to the comments store.
      response.data.sort((a, b) => a.id.compareTo(b.id));

      ref.read(commentStoreProvider.notifier).updateItems(response.data);
    }

    return response;
  }
}
