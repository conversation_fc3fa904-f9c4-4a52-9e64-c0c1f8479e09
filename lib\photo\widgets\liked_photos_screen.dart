import 'package:easy_load_more/easy_load_more.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:portraitmode/profile/services/profile_service.dart';

class LikedPhotosScreen extends ConsumerStatefulWidget {
  const LikedPhotosScreen({super.key});

  @override
  LikedPhotosScreenState createState() => LikedPhotosScreenState();
}

class LikedPhotosScreenState extends ConsumerState<LikedPhotosScreen> {
  final _scrollController = ScrollController();
  final int _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  int _loadMoreLastId = 0;
  bool _loadMoreEndReached = false;

  final _profileService = ProfileService();
  final List<PhotoData> _photoList = [];

  @override
  void dispose() {
    _scrollController.dispose();
    _blockScrollNotifier.dispose();
    super.dispose();
  }

  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    return Scaffold(
      body: SafeArea(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _blockScrollNotifier,
              builder: (context, blockScrolling, child) {
                return CustomScrollView(
                  controller: _scrollController,
                  cacheExtent: _cacheExtent,
                  physics: blockScrolling
                      ? const NeverScrollableScrollPhysics()
                      : null,
                  slivers: <Widget>[
                    PmSliverAppBar(
                      scrollController: _scrollController,
                      titleText: "Liked photos",
                      useLogo: false,
                      automaticallyImplyLeading: true,
                      actions: const [],
                    ),
                    EasyLoadMore(
                      isFinished: _loadMoreEndReached,
                      onLoadMore: _handleLoadMore,
                      loadingWidgetColor: context.colors.baseColorAlt,
                      runOnEmptyResult: true,
                      loadingStatusText: "",
                      finishedStatusText: "",
                      child: _buildSliverListView(),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSliverListView() {
    return SliverList(
      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
        return PhotoListItem(
          key: ValueKey(_photoList[index].id),
          photoId: _photoList[index].id,
          photo: _photoList[index],
          margin: EdgeInsets.only(
            top: index == 0 ? LayoutConfig.contentTopGap : 12.0,
          ),
          screenName: 'liked_photos_screen',
          onTwoFingersOn: () {
            _blockScrollNotifier.value = true;
          },
          onTwoFingersOff: () {
            _blockScrollNotifier.value = false;
          },
        );
      }, childCount: _photoList.length),
    );
  }

  Future<void> _handleRefresh() async {
    _photoList.clear();
    _loadMoreLastId = 0;
    _loadMoreEndReached = false;

    PhotoListResponse response = await _profileService.likedPhotos(
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
    );

    _handlePhotoListResponse(response);
  }

  Future<bool> _handleLoadMore() async {
    PhotoListResponse response = await _profileService.likedPhotos(
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
    );

    _handlePhotoListResponse(response);

    return response.success;
  }

  bool _handlePhotoListResponse(PhotoListResponse response) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return false;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      }

      return false;
    }

    if (response.data.isEmpty) {
      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return true;
    }

    _loadMoreLastId = response.data.last.id;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (mounted) {
      setState(() {
        _photoList.addAll(response.data);
      });
    }

    return true;
  }
}
