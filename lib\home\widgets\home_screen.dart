import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/title_with_logo.dart';
import 'package:portraitmode/home/<USER>/following_photos_tab_content.dart';
import 'package:portraitmode/home/<USER>/home_tabbar_delegate.dart';
import 'package:portraitmode/home/<USER>/latest_photos_tab_content.dart';
import 'package:portraitmode/photo/widgets/photo_detail/profile_avatar.dart';

enum SwipeDirection { left, right, none }

class HomeScreen extends ConsumerStatefulWidget {
  final int? initialIndex;
  final ValueNotifier<VoidCallback?> refreshNotifier;

  const HomeScreen({
    super.key,
    this.initialIndex,
    required this.refreshNotifier,
  });

  @override
  HomeScreenState createState() => HomeScreenState();
}

class HomeScreenState extends ConsumerState<HomeScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  double? _previousAnimationValue;
  SwipeDirection _swipeDirection = SwipeDirection.none;
  double dividerBarWidth = 2.0;

  final ValueNotifier<VoidCallback?> _latestPhotosRefreshNotifier =
      ValueNotifier(null);

  final ValueNotifier<VoidCallback?> _followingPhotosRefreshNotifier =
      ValueNotifier(null);

  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  @override
  void initState() {
    super.initState();

    widget.refreshNotifier.value = _scrollToTop;

    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.initialIndex ?? 0,
    );

    _tabController.animation?.addListener(_handleTabSwipe);
  }

  @override
  void dispose() {
    _tabController.animation?.removeListener(_handleTabSwipe);
    _tabController.dispose();
    _latestPhotosRefreshNotifier.dispose();
    _followingPhotosRefreshNotifier.dispose();
    _blockScrollNotifier.dispose();

    super.dispose();
  }

  void _handleTabSwipe() {
    if (_previousAnimationValue == null) {
      _previousAnimationValue = _tabController.animation!.value;
      return;
    }

    if (_tabController.animation!.value % 1 == 0) {
      // log("Swipe completed");

      if (mounted) {
        setState(() {
          _swipeDirection = SwipeDirection.none;
        });
      }
    } else {
      SwipeDirection direction = SwipeDirection.none;

      if (_tabController.animation!.value > _previousAnimationValue!) {
        // Handle right swipe
        // log("Swiping to the right");
        direction = SwipeDirection.right;
      } else if (_tabController.animation!.value < _previousAnimationValue!) {
        // Handle left swipe
        // log("Swiping to the left");
        direction = SwipeDirection.left;
      }

      if (mounted && direction != _swipeDirection) {
        setState(() {
          _swipeDirection = direction;
        });
      }
    }

    _previousAnimationValue = _tabController.animation!.value;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: NestedScrollView(
          floatHeaderSlivers: true,
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return [
              SliverAppBar(
                primary: true,
                automaticallyImplyLeading: false,
                floating: false,
                pinned: true,
                title: SizedBox(width: double.infinity, child: TitleWithLogo()),
                actions: [
                  Padding(
                    padding: const EdgeInsets.only(
                      right: ScreenStyleConfig.horizontalPadding,
                    ),
                    child: ProfileAvatar(size: 32.0, toProfileScreen: true),
                  ),
                ],
                bottom: PreferredSize(
                  preferredSize: Size.fromHeight(0.8),
                  child: Container(
                    height: 0.8,
                    color: context.colors.borderColor,
                  ),
                ),
              ),
              SliverPersistentHeader(
                floating: false,
                pinned: true,
                delegate: HomeTabbarDelegate(controller: _tabController),
              ),
            ];
          },
          body: ValueListenableBuilder(
            valueListenable: _blockScrollNotifier,
            builder: (context, blockScrolling, child) {
              return TabBarView(
                controller: _tabController,
                physics: blockScrolling
                    ? const NeverScrollableScrollPhysics()
                    : null,
                children: [
                  Stack(
                    children: [
                      LatestPhotosTabContent(
                        refreshNotifier: _latestPhotosRefreshNotifier,
                        onPhotoTwoFingersOn: () {
                          _blockScrollNotifier.value = true;
                        },
                        onPhotoTwoFingersOff: () {
                          _blockScrollNotifier.value = false;
                        },
                      ),
                      if (_swipeDirection == SwipeDirection.right)
                        Positioned(
                          top: 0.0,
                          right: 0.0,
                          child: Container(
                            width: dividerBarWidth,
                            height: MediaQuery.of(context).size.height,
                            color: context.colors.dividerColor,
                          ),
                        ),
                    ],
                  ),
                  Stack(
                    children: [
                      FollowingPhotosTabContent(
                        refreshNotifier: _followingPhotosRefreshNotifier,
                        onPhotoTwoFingersOn: () {
                          _blockScrollNotifier.value = true;
                        },
                        onPhotoTwoFingersOff: () {
                          _blockScrollNotifier.value = false;
                        },
                      ),
                      if (_swipeDirection == SwipeDirection.left)
                        Positioned(
                          top: 0.0,
                          left: 0.0,
                          child: Container(
                            width: dividerBarWidth,
                            height: MediaQuery.of(context).size.height,
                            color: context.colors.dividerColor,
                          ),
                        ),
                    ],
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Future<void> _scrollToTop() async {
    if (_tabController.index == 0) {
      _latestPhotosRefreshNotifier.value?.call();
      return;
    }

    if (_tabController.index == 1) {
      _followingPhotosRefreshNotifier.value?.call();
      return;
    }
  }
}
