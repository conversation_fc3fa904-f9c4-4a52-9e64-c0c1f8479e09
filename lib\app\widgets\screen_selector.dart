import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:portraitmode/app/widgets/main_screen.dart';
import 'package:portraitmode/auth/widgets/login_screen.dart';
import 'package:portraitmode/hive/services/local_auth_service.dart';
import 'package:portraitmode/hive/services/local_settings_service.dart';
import 'package:portraitmode/onboarding/onboarding_screen.dart';

class ScreenSelector extends StatelessWidget {
  const ScreenSelector({super.key});

  @override
  Widget build(BuildContext context) {
    final bool isLoggedIn = LocalAuthService.isLoggedIn;

    if (!LocalSettingsService.doneOnboarding) {
      return OnboardingScreen(isLoggedIn: isLoggedIn);
    }

    if (isLoggedIn) {
      log('🛠️ Build ScreenSelector');

      return const MainScreen();
    }

    return const LoginScreen();
  }
}
