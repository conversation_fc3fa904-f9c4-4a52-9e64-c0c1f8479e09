// mixed_photos_provider.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class MixedPhotoIdsNotifier extends IdListNotifier {}

final mixedPhotoIdsProvider =
    NotifierProvider.autoDispose<MixedPhotoIdsNotifier, List<int>>(
      MixedPhotoIdsNotifier.new,
    );

final mixedPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(mixedPhotoIdsProvider);
  if (ids.isEmpty) return <PhotoData>[];

  return ref.read(photoStoreProvider.notifier).getItems(ids);
});

/// High-level service for managing mixed photos's reactivity.
/// This is the recommended way to manage mixed photos.
final class MixedPhotosReactiveService {
  const MixedPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get mixed photos list
  List<PhotoData> getAll() {
    return ref.read(mixedPhotosProvider);
  }

  /// Get mixed photo IDs
  List<int> getAllIds() {
    return ref.read(mixedPhotoIdsProvider);
  }

  /// Add a new mixed photo (adds to both global store and mixed list)
  void addItem(PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to mixed photos list
    ref.read(mixedPhotoIdsProvider.notifier).addItem(photo.id);
  }

  /// Add multiple mixed photos
  void addItems(List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to mixed photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(mixedPhotoIdsProvider.notifier).addItems(photoIds);
  }

  /// Remove a mixed photo
  void remove(int photoId) {
    // Remove from mixed photos list
    ref.read(mixedPhotoIdsProvider.notifier).removeItem(photoId);
  }

  /// Remove multiple mixed photos
  void removeItems(List<int> photoIds) {
    if (photoIds.isEmpty) return;

    // Remove from mixed photos list
    ref.read(mixedPhotoIdsProvider.notifier).removeItems(photoIds);
  }

  /// Update a photo in the store (automatically reflects in mixed list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all mixed photos
  void replaceAll(List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace mixed photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(mixedPhotoIdsProvider.notifier).replaceAll(photoIds);
  }

  /// Clear all mixed photos
  void clear() {
    // Clear the mixed photos list
    ref.read(mixedPhotoIdsProvider.notifier).clear();
  }

  /// Get mixed photos count
  int get count => ref.read(mixedPhotoIdsProvider.notifier).length;

  /// Check if mixed list is empty
  bool get isEmpty => ref.read(mixedPhotoIdsProvider.notifier).isEmpty;

  /// Check if mixed list is not empty
  bool get isNotEmpty => ref.read(mixedPhotoIdsProvider.notifier).isNotEmpty;
}

/// Provider for the MixedPhotosReactiveService.
final mixedPhotosReactiveServiceProvider =
    Provider.autoDispose<MixedPhotosReactiveService>((ref) {
      return MixedPhotosReactiveService(ref);
    });
