import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/providers/app_state_provider.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/common/utils/common_refresh_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/featured_photos_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/utils/photo_util.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';

class FeaturedPhotoListScreen extends ConsumerStatefulWidget {
  final ValueNotifier<VoidCallback?> refreshNotifier;

  const FeaturedPhotoListScreen({super.key, required this.refreshNotifier});

  @override
  FeaturedPhotoListScreenState createState() => FeaturedPhotoListScreenState();
}

class FeaturedPhotoListScreenState
    extends ConsumerState<FeaturedPhotoListScreen> {
  final _scrollController = ScrollController();
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  late final int _profileId;

  final int _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  int _loadMoreLastId = 0;
  bool _loadMoreEndReached = false;
  bool _isLoadingMore = false;

  final _photoListService = PhotoListService();

  @override
  void initState() {
    widget.refreshNotifier.value = _scrollToTop;

    _profileId = LocalUserService.userId ?? 0;
    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();
    });

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _blockScrollNotifier.dispose();
    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) {
        _triggerLoadMore();
      }
    }
  }

  bool _canLoadMore() {
    return !_isLoadingMore && !_loadMoreEndReached;
  }

  /// Trigger load more.
  ///
  /// The `_canLoadMore` checking should be in the caller, not here.
  void _triggerLoadMore() async {
    if (mounted) {
      setState(() {
        _isLoadingMore = true;
      });
    }

    await _handleLoadMore();

    if (mounted) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    // log('build screen: FeaturedPhotoListScreen');

    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    bool behaveNewlyOpened = ref.watch(
      appStateProvider.select((data) => data.behaveNewlyOpened),
    );

    if (behaveNewlyOpened) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToTop();

        if (mounted) {
          // Reset the behaveNewlyOpened flag after handling it.
          ref.read(appStateProvider.notifier).setBehaveNewlyOpened(false);
        }
      });
    }

    final List<int> photoIdList = ref.watch(featuredPhotoIdsProvider);

    return Scaffold(
      appBar: PmAppBar(scrollController: _scrollController),
      body: SafeArea(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            key: _refreshIndicatorKey,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _blockScrollNotifier,
              builder: (context, blockScrolling, child) {
                return ListView.builder(
                  controller: _scrollController,
                  physics: blockScrolling
                      ? const NeverScrollableScrollPhysics()
                      : null,
                  cacheExtent: _cacheExtent,
                  itemCount:
                      photoIdList.length +
                      (_isLoadingMore && !_loadMoreEndReached ? 1 : 0),
                  itemBuilder: (BuildContext context, int index) {
                    if (index == photoIdList.length && _isLoadingMore) {
                      return Padding(
                        padding: EdgeInsets.only(
                          top: photoIdList.isEmpty ? 0 : 16.0,
                        ),
                        child: LinearProgressIndicator(
                          color: context.colors.baseColorAlt,
                        ),
                      );
                    }

                    return PhotoListItem(
                      key: ValueKey(photoIdList[index]),
                      photoId: photoIdList[index],
                      myProfileId: _profileId,
                      margin: EdgeInsets.only(
                        top: index == 0 ? LayoutConfig.contentTopGap : 12.0,
                      ),
                      screenName: 'featured_screen',
                      onTwoFingersOn: () {
                        _blockScrollNotifier.value = true;
                      },
                      onTwoFingersOff: () {
                        _blockScrollNotifier.value = false;
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _scrollToTop() async {
    await _scrollController.animateTo(
      -100.0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );

    if (_refreshIndicatorKey.currentState != null) {
      await _refreshIndicatorKey.currentState?.show();
    }
  }

  Future<void> _handleRefresh() async {
    _loadMoreLastId = 0;
    _loadMoreEndReached = false;

    List<dynamic> reponses = await Future.wait([
      _photoListService.fetch(
        limit: _loadMorePerPage,
        lastId: _loadMoreLastId,
        featured: true,
      ),
      CommonRefreshUtil().fetch(ref, _profileId),
    ]);

    final PhotoListResponse photoListResponse = reponses[0];

    _handlePhotoListResponse(photoListResponse, true, false);
  }

  Future<bool> _handleLoadMore() async {
    PhotoListResponse response = await _photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
      featured: true,
    );

    final isFirstLoad = _loadMoreLastId == 0;

    _handlePhotoListResponse(response, false, isFirstLoad);

    return response.success;
  }

  void _handlePhotoListResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (response.data.isEmpty) {
      if (isRefresh) {
        ref.read(featuredPhotoIdsProvider.notifier).replaceAll([]);
      }

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    _loadMoreLastId = response.data.last.id;

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      ref
          .read(featuredPhotoIdsProvider.notifier)
          .replaceAll(photoListToIdList(response.data));

      return;
    }

    if (isFirstLoad) {
      ref
          .read(featuredPhotoIdsProvider.notifier)
          .replaceAll(photoListToIdList(response.data));
      return;
    }

    ref
        .read(featuredPhotoIdsProvider.notifier)
        .addItems(photoListToIdList(response.data));
  }
}
