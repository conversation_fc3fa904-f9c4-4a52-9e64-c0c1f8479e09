import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/camera/providers/camera_photos_interaction_provider.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/providers/category_photos_interaction_provider.dart';
import 'package:portraitmode/category/providers/category_photos_provider.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/load_more/load_more_builder.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class CategoryDetailScreenListMode extends ConsumerStatefulWidget {
  const CategoryDetailScreenListMode({
    super.key,
    required this.category,
    this.initialScrollIndex = 0,
  });

  final CategoryData category;
  final int initialScrollIndex;

  @override
  CategoryDetailScreenListModeState createState() =>
      CategoryDetailScreenListModeState();
}

class CategoryDetailScreenListModeState
    extends ConsumerState<CategoryDetailScreenListMode> {
  final _scrollController = ScrollController();
  final PhotoListService photoListService = PhotoListService();
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  late final int _profileId;

  late int _loadMorePerPage;
  bool _loadMoreEndReached = false;

  // int _currentItemIndex = 0;

  @override
  void initState() {
    _loadMorePerPage = LoadMoreConfig.itemsPerPage;
    _profileId = LocalUserService.userId ?? 0;

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _blockScrollNotifier.dispose();
    super.dispose();
  }

  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    final photoList = ref.watch(categoryPhotosProvider(widget.category.id));
    const double appBarHeight = LayoutConfig.bottomNavBarHeight - 10;

    return Scaffold(
      appBar: PmAppBar(
        height: appBarHeight,
        titleText: widget.category.name,
        useLogo: false,
        automaticallyImplyLeading: true,
        scrollController: _scrollController,
      ),
      body: SafeArea(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            key: _refreshIndicatorKey,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _blockScrollNotifier,
              builder: (context, blockScrolling, child) {
                return ScrollablePositionedList.builder(
                  minCacheExtent: _cacheExtent,
                  physics: blockScrolling
                      ? const NeverScrollableScrollPhysics()
                      : null,
                  itemCount: photoList.length,
                  itemBuilder: (BuildContext context, int index) {
                    return LoadMoreBuilder(
                      isFinished: _loadMoreEndReached,
                      onLoadMore: _handleLoadMore,
                      loadingWidgetColor: context.colors.baseColorAlt,
                      idleStatusText: "",
                      loadingStatusText: "",
                      finishedStatusText: "",
                      isLastIndex:
                          (photoList.isEmpty || index == photoList.length - 1),
                      child: PhotoListItem(
                        key: ValueKey(photoList[index].id),
                        photoId: photoList[index].id,
                        photo: photoList[index],
                        margin: EdgeInsets.only(
                          top: index == 0 ? LayoutConfig.contentTopGap : 12.0,
                        ),
                        isOwnProfile: photoList[index].authorId == _profileId,
                        screenName: 'category_detail_screen',
                        onTwoFingersOn: () {
                          _blockScrollNotifier.value = true;
                        },
                        onTwoFingersOff: () {
                          _blockScrollNotifier.value = false;
                        },
                      ),
                    );
                  },
                  initialScrollIndex: widget.initialScrollIndex,
                  itemScrollController: ItemScrollController(),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    ref
        .read(cameraPhotosInteractionProvider.notifier)
        .setLoadMoreLastId(widget.category.slug, 0);

    _loadMoreEndReached = false;

    PhotoListResponse response = await photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: 0,
      categoryId: widget.category.id,
    );

    _handlePhotoListResponse(response, true, false);
  }

  Future<bool> _handleLoadMore() async {
    final int loadMoreLastId = ref
        .read(categoryPhotosInteractionProvider.notifier)
        .getLoadMoreLastId(widget.category.slug);

    final isFirstLoad = loadMoreLastId == 0;

    PhotoListResponse response = await photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: loadMoreLastId,
      categoryId: widget.category.id,
    );

    _handlePhotoListResponse(response, false, isFirstLoad);

    return response.success;
  }

  void _handlePhotoListResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final categoryPhotosReactiveService = ref.read(
      categoryPhotosReactiveServiceProvider,
    );

    if (response.data.isEmpty) {
      if (isRefresh) {
        categoryPhotosReactiveService.clear();
      }

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    ref
        .read(cameraPhotosInteractionProvider.notifier)
        .setLoadMoreLastId(widget.category.slug, response.data.last.id);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      categoryPhotosReactiveService.replaceAll(
        widget.category.id,
        response.data,
      );
    } else {
      if (isFirstLoad) {
        categoryPhotosReactiveService.replaceAll(
          widget.category.id,
          response.data,
        );
      } else {
        categoryPhotosReactiveService.addItems(
          widget.category.id,
          response.data,
        );
      }
    }
  }
}
