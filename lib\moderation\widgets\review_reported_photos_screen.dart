import 'package:easy_load_more/easy_load_more.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/moderation/dto/reported_photo_data.dart';
import 'package:portraitmode/moderation/http_responses/reported_photo_list_response.dart';
import 'package:portraitmode/moderation/services/moderation_service.dart';
import 'package:portraitmode/moderation/widgets/review_photo_list_item.dart';

class ReviewReportedPhotosScreen extends ConsumerStatefulWidget {
  const ReviewReportedPhotosScreen({super.key});

  @override
  ReviewReportedPhotosScreenState createState() =>
      ReviewReportedPhotosScreenState();
}

class ReviewReportedPhotosScreenState
    extends ConsumerState<ReviewReportedPhotosScreen> {
  final _scrollController = ScrollController();

  final int _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  int _loadMoreLastId = 0;
  bool _loadMoreEndReached = false;

  final ModerationService _moderationService = ModerationService();

  List<ReportedPhotoData> _photoList = [];

  @override
  void dispose() {
    _scrollController.dispose();
    _blockScrollNotifier.dispose();
    super.dispose();
  }

  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    return Scaffold(
      body: SafeArea(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            edgeOffset: LayoutConfig.bottomNavBarHeight,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _blockScrollNotifier,
              builder: (context, blockScrolling, child) {
                return CustomScrollView(
                  controller: _scrollController,
                  cacheExtent: _cacheExtent,
                  physics: blockScrolling
                      ? const NeverScrollableScrollPhysics()
                      : null,
                  slivers: <Widget>[
                    PmSliverAppBar(
                      scrollController: _scrollController,
                      titleText: "Reported photos",
                      useLogo: false,
                      automaticallyImplyLeading: true,
                    ),
                    EasyLoadMore(
                      isFinished: _loadMoreEndReached,
                      onLoadMore: _handleLoadMore,
                      loadingWidgetColor: context.colors.baseColorAlt,
                      runOnEmptyResult: true,
                      loadingStatusText: "",
                      finishedStatusText: "",
                      child: _buildSliverListView(),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSliverListView() {
    final int userId = LocalUserService.userId ?? 0;

    return SliverList(
      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
        final double marginTop = index == 0 ? LayoutConfig.contentTopGap : 12.0;
        final ReportedPhotoData photo = _photoList[index];

        return Container(
          margin: EdgeInsets.only(top: marginTop),
          child: ReviewPhotoListItem(
            index: index,
            photo: photo.toPhotoData(),
            isOwnPhoto: photo.authorId == userId,
            moderationType: 'photo_report',
            reporters: photo.reporters,
            onModerationDismissed: _handleModerationDismissed,
            onPhotoDeleted: _handlePhotoDeleted,
            onTwoFingersOn: () {
              _blockScrollNotifier.value = true;
            },
            onTwoFingersOff: () {
              _blockScrollNotifier.value = false;
            },
          ),
        );
      }, childCount: _photoList.length),
    );
  }

  Future<void> _handleRefresh() async {
    _photoList = [];
    _loadMoreLastId = 0;
    _loadMoreEndReached = false;

    ReportedPhotoListResponse response = await _moderationService
        .fetchReportedPhotos(limit: _loadMorePerPage, lastId: _loadMoreLastId);

    _handlePhotoListResponse(response, true, false);
  }

  Future<bool> _handleLoadMore() async {
    ReportedPhotoListResponse response = await _moderationService
        .fetchReportedPhotos(limit: _loadMorePerPage, lastId: _loadMoreLastId);

    final isFirstLoad = _loadMoreLastId == 0;

    return _handlePhotoListResponse(response, false, isFirstLoad);
  }

  bool _handlePhotoListResponse(
    ReportedPhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return false;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return false;
    }

    if (response.data.isEmpty) {
      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return true;
    }

    _loadMoreLastId = response.data.last.id;

    // Sort the array before storing it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (mounted) {
      setState(() {
        if (isRefresh) {
          _photoList = response.data;
        } else {
          if (isFirstLoad) {
            _photoList = response.data;
          } else {
            _photoList.addAll(response.data);
          }
        }
      });
    }

    return true;
  }

  void _handleModerationDismissed(int photoId) {
    int index = _photoList.indexWhere((photo) => photo.id == photoId);
    if (index == -1) return;

    if (mounted) {
      setState(() {
        _photoList = List.from(_photoList)..removeAt(index);
      });
    }
  }

  void _handlePhotoDeleted(int photoId) {
    int index = _photoList.indexWhere((photo) => photo.id == photoId);
    if (index == -1) return;

    if (mounted) {
      setState(() {
        _photoList = List.from(_photoList)..removeAt(index);
      });
    }
  }
}
