import 'package:flutter/material.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/album/widgets/add_album_modal.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/artist/widgets/artist_detail/my_profile/album_tabbar_item.dart';

class AlbumTabbar extends StatefulWidget {
  const AlbumTabbar({super.key, required this.albumList});

  final List<AlbumData> albumList;

  @override
  AlbumTabbarState createState() => AlbumTabbarState();
}

class AlbumTabbarState extends State<AlbumTabbar>
    with SingleTickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.colors.lightColor,
        border: Border(
          bottom: BorderSide(color: context.colors.borderColor, width: 1.0),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TabBar(
              isScrollable: true,
              padding: EdgeInsets.zero,
              tabAlignment: TabAlignment.start,
              dividerHeight: 0.0,
              indicatorPadding: EdgeInsets.only(bottom: -1.0),
              indicator: UnderlineTabIndicator(
                borderSide: BorderSide(
                  width: 2.7,
                  color: context.colors.brandColor,
                ),
                borderRadius:
                    BorderRadius.zero, // This removes the rounded corners
              ),
              labelColor: context.isDarkMode
                  ? context.colors.brandColor
                  : context.colors.brandColorAlt,
              unselectedLabelColor: context.colors.primarySwatch[300],
              tabs: [
                for (final album in widget.albumList)
                  AlbumTabbarItem(
                    tabbarContext: context,
                    index: widget.albumList.indexOf(album),
                    album: album,
                    isLastItem:
                        widget.albumList.indexOf(album) ==
                        widget.albumList.length - 1,
                  ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: GestureDetector(
              onTap: () => _handleAddButtonTap(),
              child: Container(
                width: 25.0,
                height: 25.0,
                decoration: BoxDecoration(
                  color: context.isDarkMode
                      ? AppColorsCache.dark().baseColorAlt
                      : AppColorsCache.light().baseColor,
                  borderRadius: const BorderRadius.all(Radius.circular(4.0)),
                ),
                child: Center(
                  child: Icon(
                    Icons.add,
                    color: context.colors.brandColor,
                    size: 16.0,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleAddButtonTap() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return AddAlbumModal(onNewAlbumAdded: _handleNewAlbumAdded);
      },
    );
  }

  void _handleNewAlbumAdded() {
    TabController tabbarController = DefaultTabController.of(context);

    final int lastIndex = tabbarController.length - 1;
    tabbarController.animateTo(lastIndex);
  }
}
