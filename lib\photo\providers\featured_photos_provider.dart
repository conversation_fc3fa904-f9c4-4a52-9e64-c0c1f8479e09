import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class FeaturedPhotoIdsNotifier extends IdListNotifier {}

final featuredPhotoIdsProvider =
    NotifierProvider.autoDispose<FeaturedPhotoIdsNotifier, List<int>>(
      FeaturedPhotoIdsNotifier.new,
    );

final featuredPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(featuredPhotoIdsProvider);
  if (ids.isEmpty) return <PhotoData>[];

  return ref.read(photoStoreProvider.notifier).getItems(ids);
});
