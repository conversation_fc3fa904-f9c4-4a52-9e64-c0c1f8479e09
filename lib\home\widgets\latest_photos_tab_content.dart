// latest_photos_tab_content.dart

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/dto/initial_data.dart';
import 'package:portraitmode/app/http_responses/initial_data_response.dart';
import 'package:portraitmode/app/providers/app_state_provider.dart';
import 'package:portraitmode/app/services/app_service.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/artist/dto/simple_artist_data.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/http_responses/category_list_response.dart';
import 'package:portraitmode/category/services/category_list_service.dart';
import 'package:portraitmode/category/widgets/category_list_slider.dart';
import 'package:portraitmode/common/utils/common_refresh_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/feedback_token/providers/feedback_token_amount_provider.dart';
import 'package:portraitmode/hive/dto/local_user_data.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/notification/providers/notification_provider.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/latest_photos_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:portraitmode/photo/widgets/trending_photo_list_slider.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

class LatestPhotosTabContent extends ConsumerStatefulWidget {
  const LatestPhotosTabContent({
    super.key,
    required this.refreshNotifier,
    this.onPhotoTwoFingersOn,
    this.onPhotoTwoFingersOff,
  });

  final ValueNotifier<VoidCallback?> refreshNotifier;
  final VoidCallback? onPhotoTwoFingersOn;
  final VoidCallback? onPhotoTwoFingersOff;

  @override
  LatestPhotosTabContentState createState() => LatestPhotosTabContentState();
}

class LatestPhotosTabContentState extends ConsumerState<LatestPhotosTabContent>
    with AutomaticKeepAliveClientMixin<LatestPhotosTabContent> {
  final _scrollController = ScrollController();
  final refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  late final int _profileId;

  final int _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  int _loadMoreLastId = 0;
  int _currentPageNumber = 0;
  bool _loadMoreEndReached = false;
  bool _isLoadingMore = false;
  final List<int> _currentlyViewedPhotoIds = [];
  List<PhotoData> _trendingPhotoList = [];

  bool _initialDataFetched = false;
  final List<CategoryData> _unfollowedCategoryList = [];

  final PhotoListService _photoListService = PhotoListService();
  final AppService _appService = AppService();
  final CategoryListService _categoryListService = CategoryListService();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    widget.refreshNotifier.value = _scrollToTop;

    _profileId = LocalUserService.userId ?? 0;
    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _blockScrollNotifier.dispose();
    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) {
        _triggerLoadMore();
      }
    }
  }

  bool _canLoadMore() {
    return !_isLoadingMore && !_loadMoreEndReached;
  }

  /// Trigger load more.
  ///
  /// The `_canLoadMore` checking should be in the caller, not here.
  void _triggerLoadMore() async {
    if (mounted) {
      setState(() {
        _isLoadingMore = true;
      });
    }

    await _handleLoadMore();

    if (mounted) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    final bool behaveNewlyOpened = ref.watch(
      appStateProvider.select((data) => data.behaveNewlyOpened),
    );

    if (behaveNewlyOpened) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToTop();

        if (mounted) {
          // Reset the behaveNewlyOpened flag after handling it.
          ref.read(appStateProvider.notifier).setBehaveNewlyOpened(false);
        }
      });
    }

    final List<int> photoIdList = ref.watch(latestPhotoIdsProvider);

    log('Building LatestPhotosTabContent');

    return Container(
      color: context.colors.scaffoldColor,
      constraints: const BoxConstraints(maxWidth: 768.0),
      child: RefreshIndicator(
        key: refreshIndicatorKey,
        onRefresh: _handleRefresh,
        child: ValueListenableBuilder(
          valueListenable: _blockScrollNotifier,
          builder: (context, blockScrolling, child) {
            return ListView.builder(
              controller: _scrollController,
              physics: blockScrolling
                  ? const NeverScrollableScrollPhysics()
                  : null,
              cacheExtent: _cacheExtent,
              itemCount:
                  photoIdList.length +
                  (_isLoadingMore && !_loadMoreEndReached ? 1 : 0),
              itemBuilder: (BuildContext context, int index) {
                if (index == photoIdList.length && _isLoadingMore) {
                  return Container(
                    padding: EdgeInsets.only(
                      top: photoIdList.isEmpty ? 0 : 16.0,
                    ),
                    child: LinearProgressIndicator(
                      color: context.colors.baseColorAlt,
                    ),
                  );
                }

                final double marginTop = index == 0
                    ? LayoutConfig.contentTopGap
                    : 12.0;

                return (index == 9 || index == 18
                    ? Column(
                        key: ValueKey(photoIdList[index]),
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                              top: marginTop,
                              bottom: 20.0,
                            ),
                            child: (index == 9
                                ? _buildCategoriesSlider()
                                : TrendingPhotoListSlider(
                                    photoList: _trendingPhotoList,
                                  )),
                          ),
                          PhotoListItem(
                            photoId: photoIdList[index],
                            myProfileId: _profileId,
                            screenName: 'explore_screen',
                            onTwoFingersOn: () {
                              _blockScrollNotifier.value = true;
                              widget.onPhotoTwoFingersOn?.call();
                            },
                            onTwoFingersOff: () {
                              _blockScrollNotifier.value = false;
                              widget.onPhotoTwoFingersOff?.call();
                            },
                          ),
                        ],
                      )
                    : PhotoListItem(
                        key: ValueKey(photoIdList[index]),
                        photoId: photoIdList[index],
                        margin: EdgeInsets.only(top: marginTop),
                        myProfileId: _profileId,
                        screenName: 'explore_screen',
                        onTwoFingersOn: () {
                          _blockScrollNotifier.value = true;
                          widget.onPhotoTwoFingersOn?.call();
                        },
                        onTwoFingersOff: () {
                          _blockScrollNotifier.value = false;
                          widget.onPhotoTwoFingersOff?.call();
                        },
                      ));
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildCategoriesSlider() {
    return CategoryListSlider(
      categoryList: _unfollowedCategoryList,
      padding: const EdgeInsets.only(top: 20.0, bottom: 22.0),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: context.isDarkMode
                ? AppColorsCache.dark().baseColorAlt
                : AppColorsCache.light().baseColor,
            width: 1.0,
          ),
          bottom: BorderSide(
            color: context.isDarkMode
                ? AppColorsCache.dark().baseColorAlt
                : AppColorsCache.light().baseColor,
            width: 1.0,
          ),
        ),
      ),
    );
  }

  void _scrollToTop() async {
    scrollListToTop(_scrollController, refreshIndicatorKey);
  }

  Future<void> _handleRefresh() async {
    _loadMoreLastId = 0;
    _loadMoreEndReached = false;
    _isLoadingMore = false;

    List<dynamic> reponses = await Future.wait([
      _photoListService.fetchRecent(
        limit: _loadMorePerPage,
        lastId: _loadMoreLastId,
      ),
      _categoryListService.fetchUnfollowed(limit: 24),
      CommonRefreshUtil().fetch(ref, _profileId),
    ]);

    final PhotoListResponse photoListResponse = reponses[0];
    final CategoryListResponse categoryListResponse = reponses[1];

    if (categoryListResponse.success) {
      _unfollowedCategoryList.clear();
      _unfollowedCategoryList.addAll(categoryListResponse.data);
    }

    _handlePhotoListResponse(photoListResponse, true);
  }

  Future<bool> _handleLoadMore() async {
    List<dynamic> responses = [];
    late PhotoListResponse photoListResponse;
    late PhotoListResponse trendingPhotoListResponse;
    bool shouldUpdateTrendingPhotos = false;
    final List<int> viewedPhotoIds = _currentPageNumber > 0
        ? _currentlyViewedPhotoIds
        : [];

    if (_currentPageNumber == 1) {
      shouldUpdateTrendingPhotos = true;

      responses = await Future.wait([
        _photoListService.fetchRecent(
          limit: _loadMorePerPage,
          lastId: _loadMoreLastId,
          viewedPhotoIds: viewedPhotoIds,
        ),
        _photoListService.fetchTrending(limit: 12),
      ]);

      photoListResponse = responses[0];
      trendingPhotoListResponse = responses[1];
    } else {
      if (_currentPageNumber == 0 && !_initialDataFetched) {
        late InitialDataResponse initialDataResponse;

        responses = await Future.wait([
          _photoListService.fetchRecent(
            limit: _loadMorePerPage,
            lastId: _loadMoreLastId,
            viewedPhotoIds: viewedPhotoIds,
          ),
          _appService.fetchInitialData(),
          CommonRefreshUtil().fetch(ref, _profileId),
        ]);

        photoListResponse = responses[0];
        initialDataResponse = responses[1];

        if (initialDataResponse.success && initialDataResponse.data != null) {
          final InitialData initialData = initialDataResponse.data!;

          if (initialData.simpleArtistData != null) {
            ref
                .read(profileProvider.notifier)
                .setTotalFollowing(
                  initialData.simpleArtistData!.totalFollowing,
                );

            SimpleArtistData simpleArtistData = initialData.simpleArtistData!;

            await LocalUserService.replace(
              LocalUserData(
                userId: simpleArtistData.id,
                nicename: simpleArtistData.nicename,
                role: simpleArtistData.role,
                displayName: simpleArtistData.displayName,
                profileUrl: simpleArtistData.profileUrl,
                avatarUrl: simpleArtistData.avatarUrl,
                membershipType: simpleArtistData.membershipType,
              ),
            );
          }

          _unfollowedCategoryList.clear();
          _unfollowedCategoryList.addAll(initialData.unfollowedCategoryList);

          if (initialData.notificationFetchResult != null) {
            ref
                .read(notificationProvider.notifier)
                .replace(initialData.notificationFetchResult!);
          }

          globalFeedbackTokensAmount = initialData.feedbackTokensAmount;

          _initialDataFetched = true;
        }
      } else {
        photoListResponse = await _photoListService.fetchRecent(
          limit: _loadMorePerPage,
          lastId: _loadMoreLastId,
          viewedPhotoIds: viewedPhotoIds,
        );
      }
    }

    _handlePhotoListResponse(photoListResponse, false);

    if (shouldUpdateTrendingPhotos &&
        mounted &&
        trendingPhotoListResponse.success &&
        trendingPhotoListResponse.data.isNotEmpty) {
      setState(() {
        _trendingPhotoList = trendingPhotoListResponse.data;
      });
    }

    return photoListResponse.success;
  }

  void _handlePhotoListResponse(PhotoListResponse response, bool isRefresh) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      _currentlyViewedPhotoIds.clear();

      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final latestPhotosReactiveService = ref.read(
      latestPhotosReactiveServiceProvider,
    );

    if (response.data.isEmpty) {
      _currentlyViewedPhotoIds.clear();

      if (isRefresh) {
        latestPhotosReactiveService.clear();
      }

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
          _isLoadingMore = false;
        });
      }

      return;
    }

    final isFirstLoad = _loadMoreLastId == 0;
    _loadMoreLastId = response.data.last.id;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    // Update the list of currently viewed photo ids.
    _currentlyViewedPhotoIds.clear();
    _currentlyViewedPhotoIds.addAll(
      response.data.map((photo) => photo.id).toList(),
    );

    if (isRefresh) {
      latestPhotosReactiveService.replaceAll(response.data);
    } else {
      if (isFirstLoad) {
        latestPhotosReactiveService.replaceAll(response.data);
      } else {
        latestPhotosReactiveService.addItems(response.data);
      }
    }

    if (mounted) {
      setState(() {
        if (isRefresh) {
          _currentPageNumber = 1;
        } else {
          _currentPageNumber = isFirstLoad ? 1 : _currentPageNumber + 1;
        }

        _isLoadingMore = false;
      });
    }
  }
}
