import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/masonry/photo_masonry_item.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';

class AlbumTabContentItem extends ConsumerStatefulWidget {
  const AlbumTabContentItem({
    super.key,
    required this.containerWidth,
    required this.artistId,
    required this.album,
    this.onAlbumRefresh,
    this.onLoadMoreStateChanged,
  });

  final double containerWidth;
  final int artistId;
  final AlbumData album;
  final Function? onAlbumRefresh;
  final Function(bool)? onLoadMoreStateChanged;

  @override
  AlbumTabContentItemState createState() => AlbumTabContentItemState();
}

class AlbumTabContentItemState extends ConsumerState<AlbumTabContentItem>
    with AutomaticKeepAliveClientMixin<AlbumTabContentItem> {
  final PhotoListService photoListService = PhotoListService();
  late NotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>>
  _myAlbumPhotoListProvider;

  static const _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  int _loadMoreLastId = 0;
  bool _loadMoreEndReached = false;
  bool _isLoadingMore = false;
  double? _loadMoreThreshold;

  @override
  bool wantKeepAlive = true;

  @override
  void initState() {
    super.initState();
    _myAlbumPhotoListProvider = getMyAlbumPhotoListProvider(widget.album.slug);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();
    });
  }

  double _getLoadMoreThreshold() {
    if (_loadMoreThreshold == null) {
      final screenHeight = MediaQuery.of(context).size.height;
      final dynamicThreshold = screenHeight * LoadMoreConfig.tresholdPercentage;

      // Add bounds by min and max.
      _loadMoreThreshold = math.max(
        math.min(dynamicThreshold, LoadMoreConfig.maxTreshold),
        LoadMoreConfig.minTreshold,
      );
    }
    return _loadMoreThreshold!;
  }

  bool _canLoadMore() {
    return !_isLoadingMore && !_loadMoreEndReached;
  }

  void _triggerLoadMore() async {
    if (_isLoadingMore || _loadMoreEndReached) {
      return;
    }

    if (mounted) {
      setState(() {
        _isLoadingMore = true;
        widget.onLoadMoreStateChanged?.call(_isLoadingMore);
      });
    }

    await _handleLoadMore();

    if (mounted) {
      setState(() {
        _isLoadingMore = false;
        widget.onLoadMoreStateChanged?.call(_isLoadingMore);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final List<PhotoData> photoList = ref.watch(_myAlbumPhotoListProvider);

    return SafeArea(
      child: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            // Handle load more when scrolling reaches the threshold
            if (scrollInfo.metrics.pixels >=
                scrollInfo.metrics.maxScrollExtent - _getLoadMoreThreshold()) {
              if (_canLoadMore()) {
                _triggerLoadMore();
              }
            }

            return false; // Allow the notification to continue
          },
          child: MasonryGridView.count(
            padding: EdgeInsets.only(
              // 8.0 - 2.7 = 5.3
              // The 2.7 is the height of LoadMoreIndicatorDelegate.
              top: 5.3,
              bottom: 8.0,
              left: 8.0,
              right: 8.0,
            ),
            crossAxisCount: 2,
            mainAxisSpacing: 8.0,
            crossAxisSpacing: 8.0,
            itemCount:
                photoList.length +
                (_isLoadingMore && !_loadMoreEndReached ? 1 : 0),
            itemBuilder: (BuildContext context, int index) {
              if (index >= photoList.length) {
                return const SizedBox.shrink();
              }

              return PhotoMasonryItem(
                key: ValueKey(photoList[index].id),
                index: index,
                photo: photoList[index],
                isOwnProfile: true,
                screenName: 'my_profile',
                onPhotoTap: () => _handlePhotoTap(photoList[index]),
              );
            },
          ),
        ),
      ),
    );
  }

  void _handlePhotoTap(PhotoData photo) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            PhotoDetailScreen(photo: photo, originScreenName: 'my_profile'),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    if (!mounted) return;

    if (widget.onAlbumRefresh != null) {
      await widget.onAlbumRefresh!();
    }

    final PhotoListResponse response = await photoListService.fetch(
      albumSlug: widget.album.slug,
      limit: _loadMorePerPage,
      lastId: 0,
      artistId: widget.artistId,
    );

    _handlePhotoListResponse(response, true);
  }

  Future<void> _handleLoadMore() async {
    final PhotoListResponse response = await photoListService.fetch(
      albumSlug: widget.album.slug,
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
      artistId: widget.artistId,
    );

    _handlePhotoListResponse(response, false);
  }

  void _handlePhotoListResponse(PhotoListResponse response, bool isRefresh) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      }

      return;
    }

    if (response.data.isEmpty) {
      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    final isFirstLoad = _loadMoreLastId == 0;
    _loadMoreLastId = response.data.last.id;

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    // Sort response.data (photo list) before consuming it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      ref.read(_myAlbumPhotoListProvider.notifier).replaceAll(response.data);
      return;
    }

    if (isFirstLoad) {
      ref.read(_myAlbumPhotoListProvider.notifier).replaceAll(response.data);
      return;
    }

    ref.read(_myAlbumPhotoListProvider.notifier).addItems(response.data);
  }
}
