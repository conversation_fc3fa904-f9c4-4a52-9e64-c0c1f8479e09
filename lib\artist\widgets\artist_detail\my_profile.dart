import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/appbar/profile_menu_indicator.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail/my_profile/album_tab_content_item.dart';
import 'package:portraitmode/artist/widgets/artist_detail/my_profile/album_tabbar_delegate.dart';
import 'package:portraitmode/artist/widgets/artist_detail/profile_header.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';
import 'package:portraitmode/settings/widgets/settings_screen.dart';

class MyProfile extends ConsumerStatefulWidget {
  const MyProfile({
    super.key,
    this.useBackButton = true,
    required this.scrollController,
    required this.containerWidth,
    required this.artist,
    this.onAlbumRefresh,
  });

  final bool useBackButton;
  final ScrollController scrollController;
  final double containerWidth;
  final ArtistData artist;
  final Function? onAlbumRefresh;

  @override
  MyProfileState createState() => MyProfileState();
}

class MyProfileState extends ConsumerState<MyProfile> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  bool _isLoadingMore = false;

  @override
  Widget build(BuildContext context) {
    final albumList = ref.watch(myAlbumProvider);

    return DefaultTabController(
      length: albumList.length,
      child: Scaffold(
        backgroundColor: context.colors.lightColor,
        body: SafeArea(
          child: NestedScrollView(
            controller: widget.scrollController,
            headerSliverBuilder:
                (BuildContext context, bool innerBoxIsScrolled) {
                  return [
                    PmSliverAppBar(
                      scrollController: widget.scrollController,
                      automaticallyImplyLeading: widget.useBackButton,
                      titleText: widget.useBackButton
                          ? '@${widget.artist.nicename}'
                          : '',
                      useLogo: widget.useBackButton ? false : true,
                      backgroundColor: context.colors.lightColor,
                      floating: false,
                      pinned: true,
                      actions: [
                        ProfileMenuIndicator(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const SettingsScreen(),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    SliverToBoxAdapter(
                      child: Container(
                        padding: const EdgeInsets.only(
                          top: LayoutConfig.contentTopGap,
                          right: ScreenStyleConfig.horizontalPadding,
                          bottom: ScreenStyleConfig.verticalPadding + 20.0,
                          left: ScreenStyleConfig.horizontalPadding,
                        ),
                        child: ProfileHeader(artist: widget.artist),
                      ),
                    ),
                    SliverPersistentHeader(
                      floating: false,
                      pinned: true,
                      delegate: AlbumTabbarDelegate(albumList: albumList),
                    ),
                    SliverAppBar(
                      primary: false,
                      automaticallyImplyLeading: false,
                      actions: [],
                      toolbarHeight: 0.0,
                      pinned: true,
                      bottom: _isLoadingMore
                          ? PreferredSize(
                              preferredSize: Size.fromHeight(2.7),
                              child: SizedBox(
                                height: 2.7,
                                child: LinearProgressIndicator(
                                  color: context.colors.baseColorAlt,
                                ),
                              ),
                            )
                          : null,
                    ),
                  ];
                },
            body: Container(
              color: context.colors.scaffoldColor,
              child: TabBarView(
                children: <AlbumTabContentItem>[
                  // Build AlbumTabContent widget using for loop.
                  for (AlbumData album in albumList)
                    AlbumTabContentItem(
                      containerWidth: widget.containerWidth,
                      artistId: widget.artist.id,
                      album: album,
                      onAlbumRefresh: widget.onAlbumRefresh,
                      onLoadMoreStateChanged: (isLoadingMore) {
                        if (_isLoadingMore == isLoadingMore) {
                          return;
                        }

                        if (mounted) {
                          setState(() {
                            _isLoadingMore = isLoadingMore;
                          });
                        }
                      },
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
