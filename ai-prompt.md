
The `ArtistsTabContent` in "lib/search/widgets/artists_tab_content.dart" has issue.

Expected behavior:
When the scrollbar of `ArtistsTabContent` TabView is scrolled-down, the `SliverAppBar` inside `NestedScrollView` in "lib/search/widgets/search_screen_active.dart" that contains `<PERSON><PERSON>ield` should be hidden.

Current behavior:
- When `ArtistsTabContent` TabView is still in loading state, and then I scroll-down the scrollbar, the `SliverAppBar` in `NestedScrollView` inside `SearchScreenActive` widget is hidden correctly.
- But when `ArtistsTabContent` TabView data has been loaded, and then I scroll down its scrollbar, the `SliverAppBar` in `NestedScrollView` inside `SearchScreenActive` wdiget stays pinned/visible.

The cause:
- When `ArtistsTabContent` tab view in loading state, the scroll is still owned by `NestedScrollView` in `SearchScreenActive` widget. So the sliver behavior works properly.
- But when `ArtistsTabContent` tab view data is loaded, what I scroll becomes the `ListView` of that `ArtistsTabContent` tab.

The solution:
Similar solution has been implemented to `CategoriesTabContent` in "lib/search/widgets/categories_tab_content.dart".
You can check for its pattern and implement the solution to `ArtistsTabContent`.

---

Currently, the load more behavior in `LatestPhotoListScreen` in "lib/photo/widgets/latest_photo_list_screen.dart" is using `EasyLoadMore` widget.

Please change it to use manual solusion (not using `EasyLoadMore`). And for the list, use `ListView.builder` instead of `SliverList`.

Similar task has been implemented to `FeaturedPhotoListScreen` in "lib/photo/widgets/featured_photo_list_screen.dart".

They are different, but you follow the load more pattern.
